import { expect, type Locator, type Page } from '@playwright/test';
import { PreHomePage } from './pre-home-page';
import { slowExpect } from '../../fixtures/ui-fixtures';
import {EnvUtils} from '../../../commons/env-utilities'

class TestCardWidget {
  readonly yourTestTitle: Locator;
  readonly upcomingTestStatus: (testDisplayNamePlusNumber: string) => Locator;
  readonly testCardName: (testDisplayNamePlusNumber: string) => Locator;
  readonly testHasStartedStatus: (testDisplayNamePlusNumber: string) => Locator;
  readonly viewSyllabusButton: (syllabus: string) => Locator;
  readonly startTestButton: (startTest: string) => Locator;


  constructor(page: Page) {
    this.yourTestTitle = page.getByText('Your tests');
    this.upcomingTestStatus = (testDisplayNamePlusNumber: string) => page.locator(`//*[text()='${testDisplayNamePlusNumber}']/ancestor::*[contains(@class,'min-w-147 w-147 rounded-2xl')]/descendant::*[text()='UPCOMING TEST']`);
    this.testCardName = (testDisplayNamePlusNumber: string) => page.getByText(`${testDisplayNamePlusNumber}`).first();
    this.testHasStartedStatus = (testDisplayNamePlusNumber: string) => page.locator(`//*[text()='${testDisplayNamePlusNumber}']/ancestor::*[contains(@class,'min-w-147 w-147 rounded-2xl')]/descendant::*[text()='TEST HAS STARTED']`);
    this.viewSyllabusButton = (syllabus: string) => page.locator(`//*[text()='${syllabus}']/ancestor::*[contains(@class,'min-w-147 w-147 rounded-2xl')]/descendant::*[text()='View Syllabus']`);
    this.startTestButton = (startTest: string) => page.locator(`//*[text()='${startTest}']/ancestor::*[contains(@class,'min-w-147 w-147 rounded-2xl')]/descendant::*[text()='Start Test']`);
    // this.viewSyllabusButton = (syllabus: string) => page.locator(`//*[text()='${syllabus}']/ancestor::*[contains(@class,'border border-primary')]/descendant::*[text()='View Syllabus']`);
    // this.startTestButton = (startTest: string) => page.locator(`//*[text()='${startTest}']/ancestor::*[contains(@class,'border border-primary')]/descendant::*[text()='Start Test']`);
  }

  async validateUpcomingTest(testDisplayNamePlusNumber) {
    await slowExpect(this.yourTestTitle, 'your test title is visible').toBeVisible();
    await expect(this.upcomingTestStatus(testDisplayNamePlusNumber), 'upcoming Test Status is visible').toBeVisible();
    await expect(this.testCardName(testDisplayNamePlusNumber), 'test card display name is visible').toBeVisible();
  }

  async viewSyllabus(testDisplayNamePlusNumber) {
    await expect(this.viewSyllabusButton(testDisplayNamePlusNumber), "verify view syllabus is visible").toBeVisible();
    await this.viewSyllabusButton(testDisplayNamePlusNumber).click();
  }

  async startTest(testDisplayNamePlusNumber) {
    await expect(this.testHasStartedStatus(testDisplayNamePlusNumber), 'test Has Started Status is visible').toBeVisible();
    await this.startTestButton(testDisplayNamePlusNumber).click();
  }
}

class LiveClassCardWidget {
  readonly yourUpcomingClassTitle: Locator;
  readonly classCardName: (className: string) => Locator;
  readonly joinNowButton: (className: string) => Locator;
  readonly joinClassButton: Locator;
  readonly classCancelledButton: (className: string) => Locator;
  readonly viewDetailsButton: (className: string) => Locator;

  constructor(page: Page) {
    this.yourUpcomingClassTitle = EnvUtils.getInstance().isProd() ? page.getByText('Your upcoming classes') : page.getByText('Schedule');
    this.classCardName = (className: string) => page.getByText(`${className}`).first();
    this.joinNowButton = (className: string) => page.locator(`//*[text()='${className}']/ancestor::*[contains(@class,'rounded-2xl bg-secondary')]/descendant::*[text()='JOIN NOW']`);
    this.joinClassButton = page.getByText('Join class');
    this.classCancelledButton = (className: string) => page.locator(`//*[text()='${className}']/ancestor::*[contains(@class,'border')]/descendant::*[text()='Class Cancelled']`);
    this.viewDetailsButton = (className: string) => page.locator(`//*[text()='${className}']/parent::*/following-sibling::button`);
  }

  async liveClassWidgets(className) {
    await expect(this.yourUpcomingClassTitle, 'your upcoming classs title is visible').toBeVisible();
    await expect(this.classCardName(className), 'live class card display name is visible').toBeVisible();
  }

  async joinLiveClass(className) {
    await this.joinNowButton(className).click();
    await expect(this.classCardName(className), 'live class card display name is visible').toBeVisible();
    await this.joinClassButton.click();
  }

  async clickJoinNowButton(className) {
    await expect(this.joinNowButton(className), 'live class join now button is visible').toBeVisible();
    await this.joinNowButton(className).click();
  }

  async clickJoinClassButton(className) {
    await expect(this.classCardName(className), 'live class card display name is visible').toBeVisible();
    await this.joinClassButton.click();
  }

  async verifyJoinClassButtonNotPresent(className) {
    await expect(this.viewDetailsButton(className), 'verify view details button is visible').toBeVisible();
    await this.viewDetailsButton(className).click();
    await expect(this.joinClassButton, 'verify join class button is visible').toBeHidden();

  }

  async verifyJoinClassButtonNotPresentRecordingClass(className) {
    await expect(this.joinNowButton(className), 'verify join now button is visible').toBeVisible();
    await this.joinNowButton(className).click();
    await expect(this.joinClassButton, 'verify join class button is visible').toBeHidden();

  }

  async classCancelled(className) {
    await expect(this.classCardName(className), 'live class card display name is visible').toBeVisible();
    await expect(this.classCancelledButton(className), 'live class cancelled card display name is visible').toBeVisible();
  }
}


export class HomePage extends PreHomePage {
  readonly exploreMenu: Locator;
  readonly doubtsMenu: Locator;
  readonly Chemistry: Locator;
  readonly Maths: Locator;
  readonly Physics: Locator;
  readonly GoodEveningText: Locator;
  readonly ContinueLearningText: Locator;
  readonly userName: Locator;
  readonly userTitle: Locator;
  readonly orderDetailsButton: Locator;
  readonly testCardWidget: TestCardWidget;
  readonly liveClassCardWidget: LiveClassCardWidget;
  readonly continueLearningJEEAdvancedText: Locator;
  readonly chemistrySubjectText: Locator;
  readonly mathsSubjectText: Locator;
  readonly physicsSubjectText: Locator;
  readonly subjectText: (subject: string) => Locator;
  readonly subjectTitle: Locator;
  readonly profileMenu: Locator;
  readonly studentNameProfileMenu: (studentName: string) => Locator;
  readonly enrolledStudentText: Locator;
  readonly noticeboardMenu: Locator;
  readonly orderDetailsMenu: Locator;
  readonly helpAndsupportMenu: Locator;
  readonly manageAddressMenu: Locator;
  readonly settingsMenu: Locator;
  readonly logoutButton: Locator;
  readonly privacyPolicyLink: Locator;
  readonly termsAndConditionsLink: Locator;
  readonly profilePageNavigationButton: Locator;
  readonly quickActionsTitleText: Locator;
  readonly studyPlannerTitleText: Locator;
  readonly testsMenu: Locator;
  readonly materialWrapper: Locator;
  readonly studentMaterial: (className: string) => Locator;
  readonly testSeriesLink: Locator;
  readonly customPracticeButton: Locator;
  readonly userNameMenu: Locator;
  readonly exploreStudyMaterialsText: Locator;
  readonly scheduleRoomId: (roomId: string) => Locator;
  readonly neetSubjectsFilter: Locator;
  readonly jeeadvancedSubjectsFilter: Locator;
  readonly saveFilter: Locator;
  readonly homeworkWidget: Locator;
  readonly homeworkText: Locator;
  readonly improvementBookButton: Locator;
  readonly flashCardButton: Locator;
  readonly continueRevisingFlashCardsTitle: Locator;
  readonly continueRevisingFlashCardsTitle1: Locator;
  readonly flashCardSubject: (subjectName: string) => Locator;
  readonly flashCardTopic: (topicName: string) => Locator;
  readonly resumeFlashCardsTitleText: Locator;
  readonly unSeenCardsSelect: Locator;
  readonly stillLearningAndUnSeenCardsSelect: Locator;
  readonly stillLearningAndUnSeenCardsSelectunSeenCardsSelect: Locator;
  readonly startRevisionButton: Locator;
  readonly notificationButton: Locator;
  readonly referAFriendMenu: Locator;

  readonly createdTestId: (testId: string) => Locator;
  readonly notificationText: Locator;
  readonly broadcastText: (broadcast: string) => Locator;
  readonly revisionNotesText: Locator;
  readonly mWemMenu: Locator;
  readonly flashcardsToBeReviewed: Locator;
  readonly viewButton: Locator;
  readonly reviseCard: Locator;
  readonly atomicStructureButton: Locator;
  readonly courseTab: Locator;
  readonly neet11Course: Locator;
  readonly neetButton: Locator;


  constructor(page: Page, isMobile: boolean) {
    super(page, isMobile);
    this.exploreMenu = page.getByText('Explore', { exact: true });
    this.doubtsMenu = page.getByTestId('dynamic_page_root').getByText('Doubts');
    this.Chemistry = page.getByRole('img', { name: 'Chemistry' });
    this.Maths = page.getByRole('img', { name: 'Maths' });
    this.Physics = page.getByRole('img', { name: 'Physics' });
    this.GoodEveningText = page.getByText('Good Evening');
    this.ContinueLearningText = page.getByText('Continue learning');
    this.userName = page.getByTestId('user-name');
    this.userTitle = page.getByTestId('user-title');
    this.orderDetailsButton = page.getByText('Order details');
    this.testCardWidget = new TestCardWidget(page);  // onces the list of cards will be added we will update to list of test cards widgets
    this.liveClassCardWidget = new LiveClassCardWidget(page);  // onces the list of cards will be added we will update to list of live class cards widgets
    this.continueLearningJEEAdvancedText = page.getByText('Continue learningJEE Advanced');
    this.chemistrySubjectText = page.getByText('Chemistry');
    this.mathsSubjectText = page.getByText('Maths');
    this.physicsSubjectText = page.getByText('Physics');
    this.subjectText = (subject: string) => page.locator(`//*[@data-testid='mySubjectGrid']//*[text()='${subject}']`);
    this.subjectTitle = page.getByTestId('subjectTitle');
    this.profileMenu = page.getByPlaceholder('menu');
    this.studentNameProfileMenu = (studentName: string) => page.locator(`//*[text()='${studentName}']`);
    this.enrolledStudentText = page.getByText('ENROLLED STUDENT');
    this.noticeboardMenu = page.getByRole('link', { name: 'Noticeboard' }).locator('visible=true').first();
    this.orderDetailsMenu = page.getByRole('link', { name: 'Order details' }).locator('visible=true').first();
    this.helpAndsupportMenu = page.getByRole('link', { name: 'Help & support' }).locator('visible=true').first();
    this.manageAddressMenu = page.getByRole('link', { name: 'Manage address' }).locator('visible=true').first();
    this.settingsMenu = page.getByText('Settings').locator('visible=true').first();
    this.logoutButton = page.getByText('Logout');
    this.privacyPolicyLink = page.getByRole('link', { name: 'PRIVACY POLICY', exact: true });
    this.termsAndConditionsLink = page.getByRole('link', { name: 'TERMS & CONDITION', exact: true });
    this.profilePageNavigationButton = page.locator("//*[@role='menu']//*[@class='w-6 h-6']").or(page.getByTestId('profile_right_arrow'));
    this.quickActionsTitleText = page.getByRole('heading', { name: 'Quick Actions' });
    this.studyPlannerTitleText = page.getByText('Study Planner').first().locator('xpath=../preceding-sibling::div');
    this.testsMenu = page.getByText('Tests', { exact: true });
    this.testSeriesLink = page.getByRole('link', { name: 'Test Series NEW' });
    this.materialWrapper = page.locator(`//*[contains(@class,"bg-secondary border-b border-primary")]`);
    this.studentMaterial = (className: string) => this.materialWrapper.locator(`//*[contains(text(),'${className}')]`).first();
    this.customPracticeButton = page.locator('p').filter({ hasText: 'Custom Practice' }).locator('xpath=../preceding-sibling::div');
    this.userNameMenu = isMobile ? page.getByTestId('user-mweb-avatar').locator('div').first().locator('visible=true') : page.getByTestId('avatar').locator('div').first().locator('visible=true');
    this.exploreStudyMaterialsText = page.getByText('Explore Study Materials').locator('visible=true');
    this.scheduleRoomId = (roomId: string) => page.getByText(`${roomId}`);
    this.neetSubjectsFilter = page.locator('div').filter({ hasText: /^Explore Study MaterialsNEET$/ }).getByRole('img');
    this.jeeadvancedSubjectsFilter = page.getByTestId('dynamic_page_root').getByText('JEE Advanced');
    this.saveFilter = page.getByTestId('dynamic_page_root').getByTestId('dls-button').getByText('SAVE');
    this.homeworkText = page.getByText('Homework');
    this.homeworkWidget = page.getByTestId('studyEssentialsCard').getByText('Homework');
    this.notificationButton = page.getByTestId('notification-icon').locator('visible=true');
    this.improvementBookButton = page.locator('p').filter({ hasText: 'Improvement Book' }).locator('xpath=../preceding-sibling::div');
    this.flashCardButton = page.locator('p').filter({ hasText: 'Flashcards' }).locator('xpath=../preceding-sibling::div');
    this.continueRevisingFlashCardsTitle = page.locator('div').filter({ hasText: /^Continue revising Flashcards$/ }).first();
    this.continueRevisingFlashCardsTitle1 = page.getByText('ChemistryAtomic StructureJust');
    this.flashCardSubject = (subjectName: string) => page.locator('#Continue_revising_').getByText(`${subjectName}`);
    this.flashCardTopic = (topicName: string) => page.getByText(`${topicName}`);
    this.resumeFlashCardsTitleText = page.getByText('Resume Flashcards');
    this.stillLearningAndUnSeenCardsSelect = page.locator('div').filter({ hasText: /^Still learning cards \+ Unseen cards$/ });
    this.unSeenCardsSelect = page.getByText('Unseen cards only');
    this.startRevisionButton = page.getByRole('button', { name: 'Start revision' });
    this.referAFriendMenu = page.getByRole('link', { name: 'Refer a Friend' })
    this.createdTestId = (testId: string) => page.getByText(`${testId}`);
    this.notificationText = page.getByText('Notifications');
    this.broadcastText = (broadcast: string) => page.getByText(`${broadcast}`).first();
    this.revisionNotesText = page.getByTestId('studyEssentialsCardsWrapper').locator('span', { hasText: 'Revision Notes' });
    this.mWemMenu = page.getByTestId('MenuIcon');
    this.flashcardsToBeReviewed = page.getByText('Flashcards to be reviewed');
    this.viewButton = page.locator('//button[@data-testid="dls-button" and .//div[text()="View"]]');
    this.reviseCard = page.locator("//button[@data-testid='dls-button' and .//div[contains(text(), 'Revise cards')]]");
    this.atomicStructureButton = page.locator("//button[@data-testid='accordion-header' and .//p[contains(text(), 'Atomic Structure')]]");
    this.courseTab = page.getByTestId('Courses');
    this.neet11Course = isMobile ? page.getByRole('button', { name: 'Class 11th' }).first(): page.getByRole('link', { name: 'Class 11th' }).first();
    this.neetButton = page.getByRole('link', { name: 'NEET', exact: true });
    
  }

  async verifySubjecstOnHomePage() {
    await expect(this.GoodEveningText, 'verifying "Good Evening" text should be visible').toBeVisible();
    await expect(this.ContinueLearningText, 'verifying "Continue Learning" text should be visible').toBeVisible();
    await expect(this.Chemistry, 'verifying "Chemistry" text should be visible').toBeVisible();
    await expect(this.Maths, 'verifying "Maths" text should be visible').toBeVisible();
    await expect(this.Physics, 'verifying "Physics" text should be visible').toBeVisible();
  }

  async validateUpcomingTestCard(testDisplayNamePlusNumber) {
    await this.testCardWidget.validateUpcomingTest(testDisplayNamePlusNumber);
    await this.testCardWidget.viewSyllabus(testDisplayNamePlusNumber);
  }

  async startTest(testDisplayNamePlusNumber) {
    await this.testCardWidget.startTest(testDisplayNamePlusNumber);
  }

  async verifyLiveClassWidgetAndJoinClass(className) {
    await this.liveClassCardWidget.liveClassWidgets(className);
    await this.liveClassCardWidget.joinLiveClass(className);
    await this.page.waitForLoadState('networkidle');
  }

  async listOfSubjectsUnderContinueLearning(subjects) {
    await slowExpect(this.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
    const keys = Object.keys(subjects);
    const count = keys.length;
    await expect(this.subjectTitle).toHaveCount(count);
    await expect(this.subjectTitle).toHaveText(keys);
  }

  async listOfSubjectsUnderContinueLearningForNeet(subjects) {
    await slowExpect(this.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
    const keys = Object.keys(subjects);
    const count = keys.length;
    await expect(this.subjectTitle).toHaveCount(count);
    await expect(this.subjectTitle).toHaveText(keys);
  }

  async verifyProfileNavigationMenu(studentName) {
    await expect(this.studentNameProfileMenu(studentName), 'verifying Student Name is visible').toBeVisible();
    await expect(this.enrolledStudentText, 'verifying enrolled student text is visible').toBeVisible();
    await expect(this.noticeboardMenu, 'verifying noticeboard menu is visible').toBeVisible();
    await expect(this.orderDetailsMenu, 'verifying order Details Menu is visible').toBeVisible();
    await expect(this.helpAndsupportMenu, 'verifying help And support Menu is visible').toBeVisible();
    await expect(this.settingsMenu, 'verifying settings Menu is visible').toBeVisible();
    await expect(this.logoutButton, 'verifying logout Button is visible').toBeVisible();
  }

  async verifyLiveClassWidget(className) {
    await this.page.waitForLoadState('networkidle');
    await this.liveClassCardWidget.liveClassWidgets(className);
  }

  async verifyLiveClassCancelled(className) {
    await this.liveClassCardWidget.liveClassWidgets(className);
    await this.liveClassCardWidget.classCancelled(className);
  }

  async clickOnJoinNowButton(className, roomId) {
    await this.liveClassCardWidget.liveClassWidgets(className);
    await expect(this.liveClassCardWidget.viewDetailsButton(className)).toBeVisible();
    await this.liveClassCardWidget.viewDetailsButton(className).click();
  }

  async verifyJoinClassButtonNotPresent(className, roomId) {
    await this.liveClassCardWidget.liveClassWidgets(className);
    await this.liveClassCardWidget.verifyJoinClassButtonNotPresentRecordingClass(className);
    // await expect(this.scheduleRoomId(roomId), 'verify scheduled room id is visible student side').toBeVisible();
  }

  async verifyJoinNowButtonNotPresent(className, roomId) {
    await this.liveClassCardWidget.liveClassWidgets(className);
    await this.liveClassCardWidget.verifyJoinClassButtonNotPresent(className);
    // await expect(this.scheduleRoomId(roomId), 'verify scheduled room id is visible student side').toBeVisible();
  }

  async verifyFileUploaded(className) {
    await expect(this.studentMaterial(className), 'Student File upload name is visible').toBeVisible();
  }

  async navigateToDoubtsPage() {
    await slowExpect(this.doubtsMenu, 'verify doubts menu is visible').toBeVisible();
    await this.doubtsMenu.click();
    await expect(this.page).toHaveURL(/.*doubts/)
  }

  async clickOnJoinClassButton(className) {
    await this.liveClassCardWidget.clickJoinClassButton(className);
  }

  async navigateToHomeworkPage() {
    await slowExpect(this.quickActionsTitleText, "verify quick actions title is visible").toBeVisible();
    if (EnvUtils.getInstance().isProd()) {
      await expect(this.homeworkText, "verify homework text is visible").toBeVisible();
    }
    await expect(this.homeworkWidget, "verify homework widget is visible").toBeVisible();
    await this.homeworkWidget.click();
  }

  async validateContinueRevisingFlashCards(subjectName, topicName) {
    await slowExpect(this.continueRevisingFlashCardsTitle, "Verify continue revising flashcards title is visible").toBeVisible();
    await expect(this.flashCardSubject(subjectName), "Verify subject name is visible").toBeVisible();
    await expect(this.flashCardTopic(topicName), "Verify topic name is visible").toBeVisible();
    await this.flashCardTopic(topicName).click();
  }

  async validateResumeFlashCardsPopUp() {
    await slowExpect(this.resumeFlashCardsTitleText, "Verify resume flashcards title is visible").toBeVisible();
    await expect(this.stillLearningAndUnSeenCardsSelect, "Verify still learning and unseen cards select is visible").toBeVisible();
    await expect(this.unSeenCardsSelect, "Verify unseen cards select is visible").toBeVisible();
  }

  async clickOnUnseenardsSelect() {
    await expect(this.unSeenCardsSelect, "Verify unseen cards select is visible").toBeVisible();
    await this.unSeenCardsSelect.click();
    await expect(this.startRevisionButton, "Verify start revision button is visible").toBeVisible();
    await this.startRevisionButton.click();

  }

  async clickOnStillLearningAndUnseenCardsSelect() {
    await expect(this.stillLearningAndUnSeenCardsSelect, "Verify unseen cards select is visible").toBeVisible();
    await this.stillLearningAndUnSeenCardsSelect.click();
    await expect(this.startRevisionButton, "Verify start revision button is visible").toBeVisible();
    await this.startRevisionButton.click();

  }

  async verifyRevisionNotesCardUnderStudyEssentials() {
    await slowExpect(this.quickActionsTitleText, "Verify quick actions title is visible").toBeVisible();
    await slowExpect(this.revisionNotesText, "Verify revision notes title is visible").toBeVisible();
    await this.revisionNotesText.click();
  }

  async verifyRevisionNotesCardUnderReviseTab(subjectName: string) {
    await slowExpect(this.subjectText(subjectName), "Verify subject text card is visible on home page").toBeVisible();
    await this.subjectText(subjectName).click();
  }

  async verifyProfileMenuTitleFields() {
    await expect(this.noticeboardMenu, 'verify notice board text is visible').toBeVisible();
    await expect(this.orderDetailsMenu, 'verify order details text is visible').toBeVisible();
    await expect(this.helpAndsupportMenu, 'verify help and support text is visible').toBeVisible();
    await expect(this.settingsMenu, 'verify settings text is visible').toBeVisible();
    await expect(this.privacyPolicyLink, 'verify privacy and policy text is visible').toBeVisible();
    await expect(this.termsAndConditionsLink, 'verify terms and conditions text is visible').toBeVisible();
  }
}
